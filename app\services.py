import requests
import base64
import os
from flask import current_app
from .models import Report
from . import db

class SyncService:
    @staticmethod
    def get_headers():
        api_key = current_app.config['CLOUD_API_KEY']
        return {'Authorization': f'Bearer {api_key}', 'Content-Type': 'application/json'}

    @staticmethod
    def sync_data():
        try:
            url = f"{current_app.config['CLOUD_API_URL']}/api/sync"
            
            # Phase 1: Push local updates to the cloud
            unsynced_reports = Report.query.filter_by(is_synced=False).all()
            reports_payload = []
            for r in unsynced_reports:
                report_data = {
                    "local_id": r.id, "cloud_id": r.cloud_id, "report_date": r.report_date.isoformat(),
                    "status": r.status, "content_section_1": r.content_section_1,
                    "content_section_2": r.content_section_2, "images": []
                }
                for image in r.images:
                    image_path = os.path.join(current_app.config['UPLOAD_FOLDER'], image.filename)
                    if os.path.exists(image_path):
                        with open(image_path, "rb") as img_file:
                            encoded_string = base64.b64encode(img_file.read()).decode('utf-8')
                            report_data["images"].append({"filename": image.filename, "data": encoded_string})
                reports_payload.append(report_data)

            payload = {"ship_id": current_app.config['SHIP_ID'], "updated_reports": reports_payload}
            response = requests.post(url, headers=SyncService.get_headers(), json=payload)
            response.raise_for_status()
            
            # Phase 2: Process response from cloud (pull updates)
            data = response.json()
            
            # Update local reports with cloud_id and synced status
            for cloud_report in data.get('synced_reports', []):
                local_report = Report.query.get(cloud_report['local_id'])
                if local_report:
                    local_report.cloud_id = cloud_report['cloud_id']
                    local_report.is_synced = True
            
            # Update local statuses from the cloud
            for status_update in data.get('status_updates', []):
                report_to_update = Report.query.filter_by(cloud_id=status_update['cloud_id']).first()
                if report_to_update and report_to_update.status != status_update['status']:
                    report_to_update.status = status_update['status']

            db.session.commit()
            return {"status": "success", "message": "Sync completed successfully."}
        
        except requests.RequestException as e:
            db.session.rollback()
            return {"status": "error", "message": f"Sync failed: {e}"}
        except Exception as e:
            db.session.rollback()
            return {"status": "error", "message": f"An unexpected error occurred: {e}"}