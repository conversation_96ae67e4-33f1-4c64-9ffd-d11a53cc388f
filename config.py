import os

basedir = os.path.abspath(os.path.dirname(__file__))

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'a-hard-to-guess-string'
    SQLALCHEMY_DATABASE_URI =  'sqlite:///' + os.path.join(basedir, 'ship_local.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    UPLOAD_FOLDER = os.path.join(basedir, 'uploads')
    CLOUD_API_URL = 'http://***********:5000' # Cloud server IP updated
    CLOUD_API_KEY = 'rsa-ship-rp'
    SHIP_ID = 1
    SHIP_SERVER_URL = 'http://***********:5000'