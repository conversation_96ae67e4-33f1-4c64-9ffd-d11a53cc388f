from app import create_app, db
from app.models import User, Report

app = create_app()

def create_default_users():
    """
    Creates default users on first run if they don't already exist.
    The 'Finalizer' role acts as the administrator for the ship app.
    """
    # Check if any users exist to prevent running this every time
    if User.query.count() > 0:
        return

    print("No users found. Creating default users...")
    
    users_to_create = [
        {'username': 'deusr1', 'password': 'pwd1', 'role': 'Finalizer'},
        {'username': 'deusr2', 'password': 'pwd2', 'role': 'DataEntry'},
        {'username': 'deusr3', 'password': 'pwd3', 'role': 'DataEntry'},
        {'username': 'deusr4', 'password': 'pwd4', 'role': 'DataEntry'},
        {'username': 'deusr5', 'password': 'pwd5', 'role': 'DataEntry'}
    ]

    for user_data in users_to_create:
        user = User(username=user_data['username'], role=user_data['role'])
        user.set_password(user_data['password'])
        db.session.add(user)
        print(f" -> Created user: {user_data['username']} ({user_data['role']})")

    db.session.commit()
    print("Default users created successfully.")


@app.shell_context_processor
def make_shell_context():
    return {'db': db, 'User': User, 'Report': Report}


if __name__ == '__main__':
    with app.app_context():
        db.create_all()  # Ensures tables exist
        create_default_users()  # Creates the 5 default users
        
    app.run(host='0.0.0.0', port=5001, debug=True)