{% extends "base.html" %}

{% block content %}
<div class="row">
    <!-- Add User Form -->
    <div class="col-md-4">
        <h3>Add New Crew Member</h3>
        <div class="card">
            <div class="card-body">
                <form method="POST" action="{{ url_for('main.add_user') }}">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="DataEntry">Data Entry</option>
                            <option value="Finalizer">Finalizer</option>
                        </select>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Add User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Existing Users List -->
    <div class="col-md-8">
        <h3>Existing Crew Members</h3>
        <div class="card">
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Role</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.username }}</td>
                            <td>{{ user.role }}</td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="2" class="text-center">No users found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}