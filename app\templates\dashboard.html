{% extends "base.html" %}
{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Reports List</h1>
    <a href="{{ url_for('main.new_report') }}" class="btn btn-primary">Create New Report</a>
</div>
<div class="card">
    <div class="card-body">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>Report Date</th>
                    <th>Status</th>
                    <th>Sync Status</th>
                </tr>
            </thead>
            <tbody>
                {% for report in reports %}
                <tr class='clickable-row' style="cursor: pointer;" data-href="{{ url_for('main.view_report', id=report.id) }}">
                    <td>{{ report.report_date.strftime('%Y-%m-%d') }}</td>
                    <td>
                        {% if report.status == 'Approved' %}
                            <span class="badge bg-success">{{ report.status }}</span>
                        {% else %}
                            <span class="badge bg-secondary">{{ report.status }}</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if report.is_synced %}
                            <span class="badge bg-success">Synced</span>
                        {% else %}
                            <span class="badge bg-warning text-dark">Pending Sync</span>
                        {% endif %}
                    </td>
                </tr>
                {% else %}
                <tr><td colspan="3" class="text-center">No reports found.</td></tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
<script>
    document.addEventListener("DOMContentLoaded", () => {
        const rows = document.querySelectorAll(".clickable-row");
        rows.forEach(row => {
            row.addEventListener("click", () => {
                window.location.href = row.dataset.href;
            });
        });
    });
</script>
{% endblock %}