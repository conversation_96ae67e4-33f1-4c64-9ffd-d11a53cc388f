from app import db, login_manager
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, index=True)
    password_hash = db.Column(db.String(255))
    role = db.Column(db.String(20), nullable=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Report(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    report_date = db.Column(db.Date, nullable=False, default=datetime.utcnow)
    # MODIFIED: Default status changed to 'Pending' for clarity
    status = db.Column(db.String(30), default='Pending', nullable=False)
    content_section_1 = db.Column(db.Text, default='')
    content_section_2 = db.Column(db.Text, default='')
    is_synced = db.Column(db.Boolean, default=False, nullable=False)
    is_editable = db.Column(db.Boolean, default=True, nullable=False)
    created_by_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    cloud_id = db.Column(db.Integer, unique=True, nullable=True)
    images = db.relationship('ImageAttachment', backref='report', lazy=True, cascade="all, delete-orphan")

class ImageAttachment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    report_id = db.Column(db.Integer, db.ForeignKey('report.id'), nullable=False)