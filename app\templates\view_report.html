{% extends "base.html" %}
{% block content %}
<div class="pdf-preview my-4">
    <div class="preview-header">
        <h1>Daily Report</h1>
        <p class="lead">Date: {{ report.report_date.strftime('%Y-%m-%d') }}</p>
        <h4>
            Status: 
            {% if report.status == 'Approved' %}
                <span class="badge bg-success">{{ report.status }}</span>
            {% else %}
                <span class="badge bg-secondary">{{ report.status }}</span>
            {% endif %}
        </h4>
    </div>
    <hr>
    <div class="preview-section">
        <h3>Engine Room Notes</h3>
        <p>{{ report.content_section_1 | safe }}</p>
    </div>
    <div class="preview-section">
        <h3>Deck Operations Log</h3>
        <p>{{ report.content_section_2 | safe }}</p>
    </div>
    <div class="preview-section">
        <h3>Attached Images</h3>
        <div class="preview-images">
            {% for image in report.images %}
                <img src="{{ url_for('main.serve_upload', filename=image.filename) }}" class="img-thumbnail" alt="Attachment">
            {% else %}
                <p>No images attached.</p>
            {% endfor %}
        </div>
    </div>
</div>

<div class="text-center my-4">
    <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary">Back to List</a>
    {% if not preview_mode %}
    <a href="{{ url_for('main.edit_report', id=report.id) }}" class="btn btn-primary">Edit Report</a>
    {% endif %}
</div>
{% endblock %}