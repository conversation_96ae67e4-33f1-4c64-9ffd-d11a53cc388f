<!DOCTYPE html>
<html lang="en">
<head>
<script>
  function f1()
  {
    fetch(CLOUD_API_URL+'/sync')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
        }
</script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ship Reporting System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .pdf-preview { background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 2rem; }
        .preview-header { text-align: center; margin-bottom: 2rem; }
        .preview-section { margin-top: 1.5rem; }
        .preview-images img { max-width: 150px; margin: 5px; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('main.dashboard') }}">🚢 Ship App</a>
            {% if current_user.is_authenticated %}
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                <li class="nav-item"><a class="nav-link" href="{{ url_for('main.dashboard') }}">Reports</a></li>
                {% if current_user.role == 'Finalizer' %}
                <li class="nav-item"><a class="nav-link" href="{{ url_for('main.manage_users') }}">Manage Users</a></li>
                {% endif %}
            </ul>
            <ul class="navbar-nav ms-auto">
                {% if current_user.role == 'Finalizer' %}
                <li class="nav-item me-2">
                <!--  <form action="{{ url_for('main.sync_with_cloud') }}" method="POST">-->
                    <form action="" method="POST">
                        <button type="submit" class="btn btn-info btn-sm">Manual Sync</button> <onclick="f1()">
                    </form>
                </li>
                {% endif %}
                <li class="nav-item"><a class="nav-link" href="{{ url_for('main.logout') }}">Logout</a></li>
            </ul>
            {% endif %}
        </div>
    </nav>
    <main class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category or 'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        {% block content %}{% endblock %}
    </main>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>