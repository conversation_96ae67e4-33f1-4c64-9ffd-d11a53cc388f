{% extends "base.html" %}
{% block content %}
<h1>{% if report %}Edit Report{% else %}Create New Report{% endif %}</h1>
<div class="card">
    <div class="card-body">
        <form method="POST" enctype="multipart/form-data" id="report-form">
            <div class="mb-3">
                <label for="report_date" class="form-label">Report Date</label>
                <input type="date" class="form-control" id="report_date" name="report_date" 
                       value="{{ report.report_date.strftime('%Y-%m-%d') if report else '' }}" required>
            </div>

            <div class="mb-3">
                <label for="content_section_1" class="form-label">Engine Room Notes</label>
                <textarea class="form-control" id="content_section_1" name="content_section_1" rows="5">{{ report.content_section_1 if report else '' }}</textarea>
            </div>

            <div class="mb-3">
                <label for="content_section_2" class="form-label">Deck Operations Log</label>
                <textarea class="form-control" id="content_section_2" name="content_section_2" rows="5">{{ report.content_section_2 if report else '' }}</textarea>
            </div>
            
            <div class="mb-3">
                <label for="images" class="form-label">Add Images</label>
                <input class="form-control" type="file" id="images" name="images" multiple>
            </div>

            {% if report and report.images %}
            <div class="mb-3">
                <p><strong>Existing Images:</strong></p>
                <div class="d-flex flex-wrap">
                    {% for image in report.images %}
                    <div class="me-2 mb-2">
                        <img src="{{ url_for('main.serve_upload', filename=image.filename) }}" class="img-thumbnail" width="100" alt="Attachment">
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <div class="mt-4">
                <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-success" formaction="{{ url_for('main.edit_report', id=report.id) if report else url_for('main.new_report') }}">Save Report</button>
                <button type="submit" class="btn btn-info" formaction="{{ url_for('main.preview_report') }}">Preview</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}