import os
from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, send_from_directory
from flask_login import login_user, logout_user, current_user, login_required
from werkzeug.utils import secure_filename
from app import db
from app.models import User, Report, ImageAttachment
from app.services import SyncService
from datetime import datetime

bp = Blueprint('main', __name__)
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def save_images_for_report(report, image_files):
    os.makedirs(current_app.config['UPLOAD_FOLDER'], exist_ok=True)
    for image_file in image_files:
        if image_file and allowed_file(image_file.filename):
            filename = secure_filename(image_file.filename)
            save_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
            image_file.save(save_path)
            new_image = ImageAttachment(filename=filename, report_id=report.id)
            db.session.add(new_image)

@bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    if request.method == 'POST':
        user = User.query.filter_by(username=request.form.get('username')).first()
        if user and user.check_password(request.form.get('password')):
            login_user(user)
            return redirect(url_for('main.dashboard'))
        else:
            flash('Invalid username or password.', 'danger')
    return render_template('login.html')

@bp.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('main.login'))

@bp.route('/')
@bp.route('/dashboard')
@login_required
def dashboard():
    reports = Report.query.order_by(Report.report_date.desc()).all()
    return render_template('dashboard.html', reports=reports)

@bp.route('/report/new', methods=['GET', 'POST'])
@login_required
def new_report():
    if request.method == 'POST':
        new_report = Report(
            report_date=datetime.strptime(request.form['report_date'], '%Y-%m-%d').date(),
            content_section_1=request.form['content_section_1'],
            content_section_2=request.form['content_section_2'],
            created_by_id=current_user.id,
            is_synced=False
        )
        db.session.add(new_report)
        db.session.commit()
        images = request.files.getlist('images')
        save_images_for_report(new_report, images)
        db.session.commit()
        flash('Report created.', 'success')
        SyncService.sync_data() # NEW: Auto-sync after creating
        return redirect(url_for('main.dashboard'))
    return render_template('report_form.html', report=None)

@bp.route('/report/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_report(id):
    report = Report.query.get_or_404(id)
    if request.method == 'POST':
        report.report_date = datetime.strptime(request.form['report_date'], '%Y-%m-%d').date()
        report.content_section_1 = request.form['content_section_1']
        report.content_section_2 = request.form['content_section_2']
        report.is_synced = False
        images = request.files.getlist('images')
        save_images_for_report(report, images)
        db.session.commit()
        flash('Report updated.', 'success')
        SyncService.sync_data() # NEW: Auto-sync after editing
        return redirect(url_for('main.view_report', id=report.id))
    return render_template('report_form.html', report=report)

@bp.route('/report/<int:id>/view')
@login_required
def view_report(id):
    report = Report.query.get_or_404(id)
    return render_template('view_report.html', report=report)

@bp.route('/report/preview', methods=['POST'])
@login_required
def preview_report():
    # Create a temporary, unsaved report object for preview
    class PreviewReport:
        def __init__(self, form_data, files):
            self.report_date = datetime.strptime(form_data['report_date'], '%Y-%m-%d').date()
            self.content_section_1 = form_data['content_section_1']
            self.content_section_2 = form_data['content_section_2']
            self.status = "Pending"
            self.images = [f for f in files if f.filename]
    
    # NOTE: This preview will not show previously saved images, only newly uploaded ones.
    # A more complex implementation could handle merging both.
    preview = PreviewReport(request.form, request.files.getlist('images'))
    return render_template('view_report.html', report=preview, preview_mode=True)

@bp.route('/uploads/<filename>')
@login_required
def serve_upload(filename):
    return send_from_directory(current_app.config['UPLOAD_FOLDER'], filename)

# User Management and Finalize routes remain the same
@bp.route('/report/<int:id>/finalize', methods=['POST'])
@login_required
def finalize_report(id):
    # ...
    return redirect(url_for('main.dashboard'))

@bp.route('/users')
@login_required
def manage_users():
    # ...
    return render_template('manage_users.html', users=User.query.all())

@bp.route('/users/add', methods=['POST'])
@login_required
def add_user():
    # ...
    return redirect(url_for('main.manage_users'))
    
@bp.route('http://192.168.1.5:5000/sync', methods=['POST'])
#@login_required
def sync_with_cloud():
    if current_user.role != 'Finalizer':
        flash('You do not have permission to perform this action.', 'danger')
        return redirect(url_for('main.dashboard'))
    result = SyncService.sync_data()
    if result['status'] == 'success':
        flash(result['message'], 'success')
    else:
        flash(result['message'], 'danger')
    return redirect(url_for('main.dashboard'))